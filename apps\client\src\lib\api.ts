import { siteConfig } from '@/config/site';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface PaginationParams {
  page?: number;
  limit?: number;
  category?: string;
  tag?: string;
  search?: string;
  sort?: 'publishedAt' | 'views' | 'likes' | 'title';
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImage?: string;
  tags: string[];
  category: string;
  status: 'draft' | 'published' | 'archived';
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
  author: string;
  publishedAt: Date;
  createdAt: Date;
  updatedAt: Date;
  views: number;
  likes: number;
}

interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color?: string;
}

interface PaginatedResponse<T> {
  posts?: T[];
  categories?: T[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Blog API methods
  async getBlogPosts(params: PaginationParams = {}): Promise<ApiResponse<PaginatedResponse<BlogPost>>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, value.toString());
      }
    });

    const queryString = searchParams.toString();
    const endpoint = `/api/blog/posts${queryString ? `?${queryString}` : ''}`;
    
    return this.request<PaginatedResponse<BlogPost>>(endpoint);
  }

  async getBlogPost(slug: string): Promise<ApiResponse<{ post: BlogPost }>> {
    return this.request<{ post: BlogPost }>(`/api/blog/posts/${slug}`);
  }

  async getBlogCategories(): Promise<ApiResponse<{ categories: BlogCategory[] }>> {
    return this.request<{ categories: BlogCategory[] }>('/api/blog/categories');
  }

  async getPopularPosts(limit: number = 5): Promise<ApiResponse<{ posts: BlogPost[] }>> {
    return this.request<{ posts: BlogPost[] }>(`/api/blog/popular?limit=${limit}`);
  }

  async getRecentPosts(limit: number = 5): Promise<ApiResponse<{ posts: BlogPost[] }>> {
    return this.request<{ posts: BlogPost[] }>(`/api/blog/recent?limit=${limit}`);
  }

  async likeBlogPost(slug: string): Promise<ApiResponse<{ likes: number }>> {
    return this.request<{ likes: number }>(`/api/blog/posts/${slug}/like`, {
      method: 'POST',
    });
  }

  // Analytics tracking
  async trackPageView(slug: string, metadata?: Record<string, any>): Promise<void> {
    try {
      await this.request('/api/analytics/pageview', {
        method: 'POST',
        body: JSON.stringify({
          slug,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          referrer: document.referrer,
          ...metadata,
        }),
      });
    } catch (error) {
      // Silently fail analytics tracking to not affect user experience
      console.warn('Analytics tracking failed:', error);
    }
  }

  async trackEvent(event: string, data?: Record<string, any>): Promise<void> {
    try {
      await this.request('/api/analytics/event', {
        method: 'POST',
        body: JSON.stringify({
          event,
          data,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
        }),
      });
    } catch (error) {
      console.warn('Event tracking failed:', error);
    }
  }

  // Newsletter API
  async subscribeToNewsletter(email: string, name?: string): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>('/api/newsletter/subscribe', {
      method: 'POST',
      body: JSON.stringify({ email, name }),
    });
  }

  // Contact API
  async sendContactMessage(data: {
    name: string;
    email: string;
    subject: string;
    message: string;
  }): Promise<ApiResponse<{ message: string }>> {
    return this.request<{ message: string }>('/api/contact/send', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types for use in components
export type { BlogPost, BlogCategory, PaginationParams, ApiResponse, PaginatedResponse };

// Utility functions for common operations
export const blogApi = {
  getPosts: (params?: PaginationParams) => apiClient.getBlogPosts(params),
  getPost: (slug: string) => apiClient.getBlogPost(slug),
  getCategories: () => apiClient.getBlogCategories(),
  getPopular: (limit?: number) => apiClient.getPopularPosts(limit),
  getRecent: (limit?: number) => apiClient.getRecentPosts(limit),
  like: (slug: string) => apiClient.likeBlogPost(slug),
};

export const analyticsApi = {
  trackPageView: (slug: string, metadata?: Record<string, any>) => 
    apiClient.trackPageView(slug, metadata),
  trackEvent: (event: string, data?: Record<string, any>) => 
    apiClient.trackEvent(event, data),
};

export const newsletterApi = {
  subscribe: (email: string, name?: string) => 
    apiClient.subscribeToNewsletter(email, name),
};

export const contactApi = {
  sendMessage: (data: {
    name: string;
    email: string;
    subject: string;
    message: string;
  }) => apiClient.sendContactMessage(data),
};
