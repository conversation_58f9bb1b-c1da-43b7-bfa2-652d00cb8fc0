import app from './app';
import { config } from './config';
import { connectToDatabase } from '@rxy/database';

const startServer = async (): Promise<void> => {
  try {
    // Try to connect to database, but don't fail if it's not available
    console.log('🔌 Attempting to connect to database...');
    try {
      await connectToDatabase(config.MONGODB_URI);
      console.log('✅ Database connected successfully');
    } catch (error) {
      console.log('⚠️  Database connection failed, running in mock mode');
      console.log('💡 Install MongoDB locally or use a cloud database for full functionality');
    }

    // Start server
    const server = app.listen(config.PORT, () => {
      console.log(`🚀 Server running on port ${config.PORT}`);
      console.log(`📍 Environment: ${config.NODE_ENV}`);
      console.log(`🌐 API URL: ${config.API_URL}`);
      console.log(`📱 Client URL: ${config.CLIENT_URL}`);
      console.log(`⚙️  Admin URL: ${config.ADMIN_URL}`);
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      server.close(() => {
        console.log('✅ HTTP server closed');
        process.exit(0);
      });

      // Force close after 10 seconds
      setTimeout(() => {
        console.error('❌ Could not close connections in time, forcefully shutting down');
        process.exit(1);
      }, 10000);
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
