'use client';

// Performance optimization utilities for animations

export const optimizeForDevice = () => {
  if (typeof window === 'undefined') return { isLowEnd: false, isMobile: false };

  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );

  // Detect low-end devices
  const isLowEnd = (() => {
    // Check for hardware concurrency (CPU cores)
    const cores = navigator.hardwareConcurrency || 1;

    // Check for device memory (if available)
    const memory = (navigator as any).deviceMemory || 4;

    // Check for connection type
    const connection = (navigator as any).connection;
    const isSlowConnection =
      connection &&
      (connection.effectiveType === 'slow-2g' ||
        connection.effectiveType === '2g' ||
        connection.effectiveType === '3g');

    return cores <= 2 || memory <= 2 || isSlowConnection;
  })();

  return { isLowEnd, isMobile };
};

export const getAnimationConfig = () => {
  const { isLowEnd, isMobile } = optimizeForDevice();

  return {
    // Reduce animation complexity on low-end devices
    enableComplexAnimations: !isLowEnd,
    enableParallax: !isLowEnd,
    enable3D: !isLowEnd && !isMobile,

    // Adjust animation durations
    animationDuration: isLowEnd ? 0.3 : 0.8,
    parallaxIntensity: isLowEnd ? 0.3 : 1,

    // Frame rate targets
    targetFPS: isMobile ? 30 : 60,

    // Reduce particle counts
    particleCount: isLowEnd ? 5 : isMobile ? 10 : 20,
  };
};

// Throttle function for performance-sensitive operations
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Debounce function for resize events
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(this, args), wait);
  };
};

// Check if user prefers reduced motion
export const prefersReducedMotion = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Responsive breakpoint utilities
export const getViewportSize = () => {
  if (typeof window === 'undefined') return { width: 1024, height: 768 };
  return { width: window.innerWidth, height: window.innerHeight };
};

export const getBreakpoint = () => {
  const { width } = getViewportSize();

  if (width >= 2560) return 'ultrawide';
  if (width >= 1920) return '2xl';
  if (width >= 1440) return 'xl';
  if (width >= 1024) return 'lg';
  if (width >= 768) return 'md';
  if (width >= 640) return 'sm';
  return 'xs';
};

export const getResponsiveConfig = () => {
  const breakpoint = getBreakpoint();
  const { isLowEnd, isMobile } = optimizeForDevice();

  return {
    breakpoint,
    maxWidth: {
      xs: '100%',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1400px',
      '2xl': '1600px',
      ultrawide: '1800px',
    }[breakpoint],

    // Parallax intensity based on screen size
    parallaxIntensity:
      {
        xs: 0.1,
        sm: 0.2,
        md: 0.4,
        lg: 0.6,
        xl: 0.8,
        '2xl': 1.0,
        ultrawide: 1.2,
      }[breakpoint] * (isLowEnd ? 0.3 : 1),

    // Animation complexity
    enableComplexAnimations: !isLowEnd && !isMobile,
    enable3D: !isLowEnd && breakpoint !== 'xs' && breakpoint !== 'sm',

    // Content spacing
    containerPadding: {
      xs: '1rem',
      sm: '1.5rem',
      md: '2rem',
      lg: '2rem',
      xl: '3rem',
      '2xl': '3rem',
      ultrawide: '4rem',
    }[breakpoint],
  };
};

// Optimize will-change CSS property usage
export const optimizeWillChange = (element: HTMLElement, properties: string[]) => {
  element.style.willChange = properties.join(', ');

  // Remove will-change after animation completes
  const cleanup = () => {
    element.style.willChange = 'auto';
  };

  return cleanup;
};

// Intersection Observer for performance
export const createIntersectionObserver = (
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
) => {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '0px',
    threshold: 0.1,
    ...options,
  };

  return new IntersectionObserver(callback, defaultOptions);
};
