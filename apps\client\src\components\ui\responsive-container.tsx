'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  as?: keyof JSX.IntrinsicElements;
}

// Centralized max-width system matching the established constraints
const containerSizes = {
  sm: 'max-w-2xl',
  md: 'max-w-4xl', 
  lg: 'max-w-6xl',
  xl: 'max-w-[1200px] xl:max-w-[1400px] 2xl:max-w-[1600px]', // Matches hero content
  full: 'max-w-none',
};

export function ResponsiveContainer({ 
  children, 
  className = '', 
  size = 'xl',
  as: Component = 'div' 
}: ResponsiveContainerProps) {
  return (
    <Component 
      className={cn(
        'mx-auto px-4 sm:px-6 lg:px-8 xl:px-12',
        containerSizes[size],
        className
      )}
    >
      {children}
    </Component>
  );
}

// Container for absolutely positioned elements that need to respect content boundaries
export function BoundedContainer({ 
  children, 
  className = '',
  size = 'xl' 
}: ResponsiveContainerProps) {
  return (
    <div className={cn(
      'absolute inset-0 flex justify-center',
      className
    )}>
      <div className={cn(
        'relative w-full',
        containerSizes[size],
        'px-4 sm:px-6 lg:px-8 xl:px-12'
      )}>
        {children}
      </div>
    </div>
  );
}

// Hook to get container dimensions for calculations
export function useContainerBounds() {
  const getContainerWidth = () => {
    if (typeof window === 'undefined') return 1200;
    
    const width = window.innerWidth;
    const padding = width >= 1280 ? 96 : width >= 1024 ? 64 : width >= 640 ? 48 : 32; // xl:px-12, lg:px-8, sm:px-6, px-4
    
    if (width >= 1536) return Math.min(1600 - padding, width - padding); // 2xl
    if (width >= 1280) return Math.min(1400 - padding, width - padding); // xl
    return Math.min(1200 - padding, width - padding); // default
  };

  const getRelativePosition = (percentage: number) => {
    const containerWidth = getContainerWidth();
    return (containerWidth * percentage) / 100;
  };

  return {
    containerWidth: getContainerWidth(),
    getRelativePosition,
    // Helper functions for common positioning
    leftQuarter: () => getRelativePosition(25),
    leftThird: () => getRelativePosition(33),
    center: () => getRelativePosition(50),
    rightThird: () => getRelativePosition(67),
    rightQuarter: () => getRelativePosition(75),
  };
}
