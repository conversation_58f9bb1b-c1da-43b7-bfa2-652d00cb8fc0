'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { redirect } from 'next/navigation';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Eye,
  Users,
  Clock,
  MousePointer,
  Globe,
  Smartphone,
  Monitor,
  Calendar,
  Download,
} from 'lucide-react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { adminApi } from '@/lib/api';

interface AnalyticsData {
  overview: {
    totalPageViews: number;
    uniqueVisitors: number;
    bounceRate: number;
    avgSessionDuration: number;
    totalSessions: number;
    newVisitors: number;
    returningVisitors: number;
  };
  pageViews: Array<{
    date: string;
    views: number;
    uniqueVisitors: number;
  }>;
  topPages: Array<{
    path: string;
    views: number;
    title: string;
  }>;
  topBlogPosts: Array<{
    id: string;
    title: string;
    slug: string;
    views: number;
    likes: number;
    shares: number;
    avgReadTime: number;
    bounceRate: number;
  }>;
  referrers: Array<{
    source: string;
    visits: number;
    percentage: number;
  }>;
  devices: Array<{
    type: string;
    visits: number;
    percentage: number;
  }>;
  browsers: Array<{
    name: string;
    visits: number;
    percentage: number;
  }>;
  countries: Array<{
    name: string;
    visits: number;
    percentage: number;
  }>;
}

export default function AnalyticsDashboard() {
  const { data: session, status } = useSession();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('overview');

  useEffect(() => {
    if (status === 'unauthenticated') {
      redirect('/auth/signin');
    }
  }, [status]);

  useEffect(() => {
    if (session?.user?.role !== 'admin') {
      redirect('/auth/signin');
    }
  }, [session]);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        
        const params: any = {};
        if (selectedMetric !== 'overview') {
          params.metric = selectedMetric;
        }

        // Add date range if needed
        if (dateRange !== 'all') {
          const endDate = new Date();
          const startDate = new Date();
          
          switch (dateRange) {
            case '7d':
              startDate.setDate(endDate.getDate() - 7);
              break;
            case '30d':
              startDate.setDate(endDate.getDate() - 30);
              break;
            case '90d':
              startDate.setDate(endDate.getDate() - 90);
              break;
          }
          
          params.startDate = startDate.toISOString();
          params.endDate = endDate.toISOString();
        }

        const response = await adminApi.getAnalytics(params);
        
        if (response.success) {
          setAnalyticsData(response.data);
        }
      } catch (error) {
        console.error('Failed to fetch analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    if (session?.user?.role === 'admin') {
      fetchAnalytics();
    }
  }, [session, dateRange, selectedMetric]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getChangeIndicator = (value: number) => {
    if (value > 0) {
      return (
        <span className="inline-flex items-center text-green-600">
          <TrendingUp className="h-4 w-4 mr-1" />
          +{value}%
        </span>
      );
    } else if (value < 0) {
      return (
        <span className="inline-flex items-center text-red-600">
          <TrendingDown className="h-4 w-4 mr-1" />
          {value}%
        </span>
      );
    }
    return <span className="text-gray-500">0%</span>;
  };

  if (status === 'loading' || loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading analytics...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (!analyticsData) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-600">Failed to load analytics data.</p>
        </div>
      </AdminLayout>
    );
  }

  const overviewStats = [
    {
      name: 'Page Views',
      value: analyticsData.overview.totalPageViews,
      icon: Eye,
      change: 18,
      color: 'bg-blue-500',
    },
    {
      name: 'Unique Visitors',
      value: analyticsData.overview.uniqueVisitors,
      icon: Users,
      change: 12,
      color: 'bg-green-500',
    },
    {
      name: 'Avg. Session',
      value: analyticsData.overview.avgSessionDuration,
      icon: Clock,
      change: -5,
      color: 'bg-yellow-500',
      format: 'duration',
    },
    {
      name: 'Bounce Rate',
      value: analyticsData.overview.bounceRate,
      icon: MousePointer,
      change: -8,
      color: 'bg-red-500',
      format: 'percentage',
    },
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600">Track your website performance and user engagement</p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="all">All time</option>
            </select>
            
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {overviewStats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`${stat.color} rounded-md p-3`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {stat.format === 'duration' 
                      ? formatDuration(stat.value)
                      : stat.format === 'percentage'
                      ? `${stat.value}%`
                      : formatNumber(stat.value)
                    }
                  </p>
                </div>
              </div>
              <div className="mt-4">
                {getChangeIndicator(stat.change)}
                <span className="text-sm text-gray-500 ml-2">vs last period</span>
              </div>
            </div>
          ))}
        </div>

        {/* Charts and Data */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Page Views Chart */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Page Views Over Time</h2>
            <div className="h-64 flex items-end justify-between space-x-2">
              {analyticsData.pageViews.map((item, index) => {
                const maxViews = Math.max(...analyticsData.pageViews.map(d => d.views));
                const height = (item.views / maxViews) * 100;
                
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div
                      className="w-full bg-blue-500 rounded-t"
                      style={{ height: `${height}%` }}
                      title={`${item.views} views on ${item.date}`}
                    />
                    <span className="text-xs text-gray-500 mt-2">
                      {new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Top Pages */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Top Pages</h2>
            <div className="space-y-3">
              {analyticsData.topPages.map((page, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{page.title}</p>
                    <p className="text-sm text-gray-500 truncate">{page.path}</p>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {formatNumber(page.views)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Blog Posts */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Top Blog Posts</h2>
            <div className="space-y-4">
              {analyticsData.topBlogPosts.map((post, index) => (
                <div key={index} className="border-b border-gray-200 pb-3 last:border-b-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">{post.title}</p>
                      <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>{formatNumber(post.views)} views</span>
                        <span>{post.likes} likes</span>
                        <span>{formatDuration(post.avgReadTime)} read time</span>
                      </div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">
                      {post.bounceRate}% bounce
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Traffic Sources */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Traffic Sources</h2>
            <div className="space-y-3">
              {analyticsData.referrers.map((referrer, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <Globe className="h-4 w-4 text-gray-400" />
                    </div>
                    <span className="text-sm font-medium text-gray-900">{referrer.source}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{referrer.percentage}%</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatNumber(referrer.visits)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Device Types */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Device Types</h2>
            <div className="space-y-3">
              {analyticsData.devices.map((device, index) => {
                const Icon = device.type === 'Desktop' ? Monitor : device.type === 'Mobile' ? Smartphone : Monitor;
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <Icon className="h-4 w-4 text-gray-400" />
                      </div>
                      <span className="text-sm font-medium text-gray-900">{device.type}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">{device.percentage}%</span>
                      <span className="text-sm font-medium text-gray-900">
                        {formatNumber(device.visits)}
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Geographic Data */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Top Countries</h2>
            <div className="space-y-3">
              {analyticsData.countries.map((country, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-900">{country.name}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{country.percentage}%</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatNumber(country.visits)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Real-time Activity */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">Real-time Activity</h2>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Live</span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-900">127</p>
              <p className="text-sm text-gray-600">Active Users</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-900">2,340</p>
              <p className="text-sm text-gray-600">Page Views (24h)</p>
            </div>
            <div className="text-center">
              <p className="text-3xl font-bold text-gray-900">45</p>
              <p className="text-sm text-gray-600">Active Sessions</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
