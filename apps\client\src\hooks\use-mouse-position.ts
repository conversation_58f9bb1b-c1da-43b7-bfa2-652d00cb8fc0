'use client';

import { useEffect, useState } from 'react';

interface MousePosition {
  x: number;
  y: number;
}

export function useMousePosition(): MousePosition {
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      setMousePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', updateMousePosition);
    return () => window.removeEventListener('mousemove', updateMousePosition);
  }, []);

  return mousePosition;
}

export function useMouseParallax(strength: number = 0.05) {
  const mousePosition = useMousePosition();
  
  const parallaxX = (mousePosition.x - window.innerWidth / 2) * strength;
  const parallaxY = (mousePosition.y - window.innerHeight / 2) * strength;
  
  return { x: parallaxX, y: parallaxY };
}
