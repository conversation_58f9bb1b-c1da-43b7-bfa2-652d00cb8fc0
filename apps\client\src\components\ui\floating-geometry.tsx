'use client';

import { useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useTheme } from 'next-themes';
import { useMousePosition } from '@/hooks/use-mouse-position';
import { useReducedMotion } from '@/hooks/use-reduced-motion';

interface FloatingGeometryProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  intensity?: number;
}

export function FloatingGeometry({
  className = '',
  size = 'md',
  intensity = 1,
}: FloatingGeometryProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { theme } = useTheme();
  const mousePosition = useMousePosition();
  const { scrollY } = useScroll();
  const prefersReducedMotion = useReducedMotion();

  // Enhanced scroll-based transformations for more noticeable parallax
  const rotationY = useTransform(scrollY, [0, 1500], [0, 720 * intensity]); // More rotation
  const rotationX = useTransform(scrollY, [0, 1200], [0, 360 * intensity]); // More rotation
  const scale = useTransform(scrollY, [0, 800], [1, 0.6]); // More dramatic scale
  const translateY = useTransform(scrollY, [0, 1000], [0, -200 * intensity]); // Vertical movement
  const opacity = useTransform(scrollY, [0, 600], [1, 0.3]); // Fade out effect

  // Size configurations
  const sizeConfig = {
    sm: { width: 200, height: 200, shapeSize: 30 },
    md: { width: 300, height: 300, shapeSize: 40 },
    lg: { width: 400, height: 400, shapeSize: 50 },
  };

  const config = sizeConfig[size];

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || prefersReducedMotion) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width * window.devicePixelRatio;
      canvas.height = rect.height * window.devicePixelRatio;
      ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    let animationId: number;

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const centerX = config.width / 2;
      const centerY = config.height / 2;

      // Mouse influence (reduced for subtlety)
      const mouseInfluenceX = (mousePosition.x - window.innerWidth / 2) * 0.02 * intensity;
      const mouseInfluenceY = (mousePosition.y - window.innerHeight / 2) * 0.02 * intensity;

      const time = Date.now() * 0.001;

      // Main floating hexagon
      ctx.save();
      ctx.translate(centerX + mouseInfluenceX, centerY + mouseInfluenceY);
      ctx.rotate(time * 0.3 * intensity);

      // Create gradient based on theme
      const gradient = ctx.createLinearGradient(
        -config.shapeSize,
        -config.shapeSize,
        config.shapeSize,
        config.shapeSize
      );

      if (theme === 'dark') {
        gradient.addColorStop(0, 'rgba(255, 107, 107, 0.4)'); // Electric Coral
        gradient.addColorStop(0.5, 'rgba(20, 184, 166, 0.3)'); // Teal Mist
        gradient.addColorStop(1, 'rgba(15, 23, 42, 0.2)'); // Midnight Blue
      } else {
        gradient.addColorStop(0, 'rgba(255, 107, 107, 0.3)');
        gradient.addColorStop(0.5, 'rgba(20, 184, 166, 0.2)');
        gradient.addColorStop(1, 'rgba(15, 23, 42, 0.1)');
      }

      ctx.fillStyle = gradient;
      ctx.strokeStyle = theme === 'dark' ? 'rgba(255, 107, 107, 0.6)' : 'rgba(15, 23, 42, 0.4)';
      ctx.lineWidth = 2;

      // Draw hexagon
      ctx.beginPath();
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = Math.cos(angle) * config.shapeSize;
        const y = Math.sin(angle) * config.shapeSize;
        if (i === 0) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
      }
      ctx.closePath();
      ctx.fill();
      ctx.stroke();

      // Inner hexagon for depth
      ctx.beginPath();
      for (let i = 0; i < 6; i++) {
        const angle = (i * Math.PI) / 3;
        const x = Math.cos(angle) * (config.shapeSize * 0.6);
        const y = Math.sin(angle) * (config.shapeSize * 0.6);
        if (i === 0) ctx.moveTo(x, y);
        else ctx.lineTo(x, y);
      }
      ctx.closePath();
      ctx.strokeStyle = theme === 'dark' ? 'rgba(20, 184, 166, 0.8)' : 'rgba(15, 23, 42, 0.6)';
      ctx.lineWidth = 1;
      ctx.stroke();

      ctx.restore();

      // Orbiting particles
      for (let i = 0; i < 3; i++) {
        ctx.save();
        const orbitRadius = config.shapeSize * 1.5;
        const orbitSpeed = 0.5 + i * 0.2;
        const angle = time * orbitSpeed + (i * Math.PI * 2) / 3;

        const x = centerX + Math.cos(angle) * orbitRadius + mouseInfluenceX * 0.5;
        const y = centerY + Math.sin(angle) * orbitRadius + mouseInfluenceY * 0.5;

        ctx.translate(x, y);

        const particleGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, 4);
        particleGradient.addColorStop(
          0,
          theme === 'dark' ? 'rgba(20, 184, 166, 0.8)' : 'rgba(15, 23, 42, 0.6)'
        );
        particleGradient.addColorStop(1, 'rgba(20, 184, 166, 0)');

        ctx.fillStyle = particleGradient;
        ctx.beginPath();
        ctx.arc(0, 0, 4, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();
      }

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, [theme, mousePosition, config, intensity, prefersReducedMotion]);

  if (prefersReducedMotion) {
    // Static fallback for reduced motion
    return (
      <div
        className={`${className} flex items-center justify-center`}
        style={{ width: config.width, height: config.height }}
      >
        <div
          className="border-2 border-primary/30 bg-gradient-to-br from-primary/10 to-accent/10"
          style={{
            width: config.shapeSize * 2,
            height: config.shapeSize * 2,
            clipPath: 'polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)',
          }}
        />
      </div>
    );
  }

  return (
    <motion.div
      className={`${className} pointer-events-none`}
      style={{
        rotateY: rotationY,
        rotateX: rotationX,
        scale,
        y: translateY,
        opacity,
      }}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 2, delay: 0.5, type: 'spring', stiffness: 100 }}
    >
      <canvas
        ref={canvasRef}
        width={config.width}
        height={config.height}
        className="w-full h-full"
        style={{
          width: config.width,
          height: config.height,
          filter: 'drop-shadow(0 0 20px rgba(255, 107, 107, 0.3))',
        }}
      />
    </motion.div>
  );
}
