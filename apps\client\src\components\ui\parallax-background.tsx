'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useTheme } from 'next-themes';
import { useReducedMotion } from '@/hooks/use-reduced-motion';
import { BoundedContainer, useContainerBounds } from './responsive-container';

interface ParallaxBackgroundProps {
  children?: React.ReactNode;
}

export function ParallaxBackground({ children }: ParallaxBackgroundProps) {
  const { scrollY } = useScroll();
  const { theme } = useTheme();
  const prefersReducedMotion = useReducedMotion();
  const { leftQuarter, rightQuarter, leftThird, rightThird, center } = useContainerBounds();

  // Enhanced parallax speeds for more noticeable layered effect
  const y1 = useTransform(scrollY, [0, 1500], [0, prefersReducedMotion ? 0 : -150]); // Slowest background
  const y2 = useTransform(scrollY, [0, 1200], [0, prefersReducedMotion ? 0 : -300]); // Medium background
  const y3 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : -500]); // Fastest background
  const y4 = useTransform(scrollY, [0, 800], [0, prefersReducedMotion ? 0 : 100]); // Reverse parallax

  // More dramatic opacity changes for better visual effect
  const opacity1 = useTransform(scrollY, [0, 400], [1, 0.2]);
  const opacity2 = useTransform(scrollY, [0, 600], [1, 0.1]);
  const opacity3 = useTransform(scrollY, [0, 800], [1, 0.05]);

  // Enhanced scale transformations
  const scale1 = useTransform(scrollY, [0, 1000], [1, prefersReducedMotion ? 1 : 1.3]);
  const scale2 = useTransform(scrollY, [0, 1000], [1, prefersReducedMotion ? 1 : 1.15]);

  // Rotation for added depth
  const rotate1 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : 5]);
  const rotate2 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : -3]);

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background layer 1 - Slowest, largest elements - Bounded within content container */}
      <BoundedContainer>
        <motion.div
          className="absolute inset-0"
          style={{ y: y1, opacity: opacity1, scale: scale1, rotate: rotate1 }}
        >
          <div
            className="absolute w-96 h-96 bg-gradient-to-br from-primary/15 via-accent/8 to-transparent rounded-full blur-3xl"
            style={{ top: '5rem', left: '2rem' }}
          />
          <div
            className="absolute w-80 h-80 bg-gradient-to-br from-accent/15 via-secondary/8 to-transparent rounded-full blur-2xl"
            style={{ top: '10rem', right: '2rem' }}
          />
          <div
            className="absolute w-64 h-64 bg-gradient-to-br from-secondary/15 via-primary/8 to-transparent rounded-full blur-3xl"
            style={{ bottom: '5rem', left: `${leftThird()}px` }}
          />
          <div
            className="absolute w-72 h-72 bg-gradient-to-br from-primary/10 via-accent/5 to-transparent rounded-full blur-3xl"
            style={{ top: '50%', right: `${rightQuarter()}px`, transform: 'translateY(-50%)' }}
          />
        </motion.div>
      </BoundedContainer>

      {/* Background layer 2 - Medium speed, medium elements - Bounded within content container */}
      <BoundedContainer>
        <motion.div
          className="absolute inset-0"
          style={{ y: y2, opacity: opacity2, scale: scale2, rotate: rotate2 }}
        >
          <div
            className="absolute w-48 h-48 bg-gradient-to-br from-accent/20 via-primary/12 to-transparent rounded-full blur-2xl"
            style={{ top: '15rem', left: `${leftQuarter()}px` }}
          />
          <div
            className="absolute w-56 h-56 bg-gradient-to-br from-primary/20 via-secondary/12 to-transparent rounded-full blur-xl"
            style={{ bottom: '10rem', right: `${rightThird()}px` }}
          />
          <div
            className="absolute w-40 h-40 bg-gradient-to-br from-secondary/20 via-accent/12 to-transparent rounded-full blur-2xl"
            style={{ top: '33%', right: '1rem' }}
          />
          <div
            className="absolute w-44 h-44 bg-gradient-to-br from-accent/18 via-primary/10 to-transparent rounded-full blur-2xl"
            style={{ bottom: '33%', left: '20%' }}
          />
        </motion.div>
      </BoundedContainer>

      {/* Background layer 3 - Fastest, smallest elements - Bounded within content container */}
      <BoundedContainer>
        <motion.div className="absolute inset-0" style={{ y: y3, opacity: opacity3 }}>
          <div
            className="absolute w-24 h-24 bg-primary/25 rounded-full blur-xl"
            style={{ top: '8rem', right: `${rightQuarter()}px` }}
          />
          <div
            className="absolute w-32 h-32 bg-accent/25 rounded-full blur-lg"
            style={{ bottom: '8rem', left: `${leftQuarter()}px` }}
          />
          <div
            className="absolute w-20 h-20 bg-secondary/25 rounded-full blur-xl"
            style={{ top: '67%', left: '1rem' }}
          />
          <div
            className="absolute w-28 h-28 bg-primary/20 rounded-full blur-lg"
            style={{ bottom: '25%', right: '2rem' }}
          />
          <div
            className="absolute w-16 h-16 bg-accent/30 rounded-full blur-md"
            style={{ top: '25%', left: `${center()}px`, transform: 'translateX(-50%)' }}
          />
          <div
            className="absolute w-6 h-6 bg-secondary/25 rounded-full blur-lg"
            style={{ bottom: '50%', right: '20%' }}
          />
        </motion.div>
      </BoundedContainer>

      {/* Animated grid pattern with parallax - Bounded within content container */}
      <BoundedContainer>
        <motion.div className="absolute inset-0 opacity-[0.02]" style={{ y: y4 }}>
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `
                radial-gradient(circle at 1px 1px, ${
                  theme === 'dark' ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.2)'
                } 1px, transparent 0),
                linear-gradient(90deg, ${
                  theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                } 1px, transparent 1px),
                linear-gradient(${
                  theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
                } 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px, 50px 50px, 50px 50px',
            }}
          />
        </motion.div>
      </BoundedContainer>

      {/* Floating geometric shapes - Bounded within content container */}
      <BoundedContainer>
        <motion.div className="absolute inset-0" style={{ y: y2 }}>
          {/* Top left floating shape */}
          <motion.div
            className="absolute w-4 h-4 border border-primary/30"
            style={{
              top: '25%',
              left: '16.67%',
              clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
            }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: 'linear',
            }}
          />

          {/* Top right floating shape */}
          <motion.div
            className="absolute w-6 h-6 border border-accent/30 rounded-full"
            style={{
              top: '33%',
              right: '25%',
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.7, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />

          {/* Bottom floating shapes */}
          <motion.div
            className="absolute w-3 h-3 bg-secondary/30 rounded-sm"
            style={{
              bottom: '33%',
              left: '33%',
            }}
            animate={{
              rotate: [0, 45, 0],
              scale: [1, 1.5, 1],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </motion.div>
      </BoundedContainer>

      {/* Subtle light rays effect - Bounded within content container */}
      <BoundedContainer>
        <motion.div className="absolute inset-0 pointer-events-none" style={{ opacity: opacity1 }}>
          <div
            className="absolute top-0 w-px h-full bg-gradient-to-b from-transparent via-primary/10 to-transparent transform -skew-x-12"
            style={{ left: '25%' }}
          />
          <div
            className="absolute top-0 w-px h-full bg-gradient-to-b from-transparent via-accent/10 to-transparent transform skew-x-12"
            style={{ right: '33%' }}
          />
        </motion.div>
      </BoundedContainer>

      {children}
    </div>
  );
}
