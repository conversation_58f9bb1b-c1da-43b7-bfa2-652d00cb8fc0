'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useTheme } from 'next-themes';
import { useReducedMotion } from '@/hooks/use-reduced-motion';

interface ParallaxBackgroundProps {
  children?: React.ReactNode;
}

export function ParallaxBackground({ children }: ParallaxBackgroundProps) {
  const { scrollY } = useScroll();
  const { theme } = useTheme();
  const prefersReducedMotion = useReducedMotion();
  
  // Different parallax speeds for layered effect
  const y1 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : -100]);
  const y2 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : -200]);
  const y3 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : -300]);
  const y4 = useTransform(scrollY, [0, 1000], [0, prefersReducedMotion ? 0 : 50]);
  
  const opacity1 = useTransform(scrollY, [0, 300], [1, 0.3]);
  const opacity2 = useTransform(scrollY, [0, 500], [1, 0.2]);
  const opacity3 = useTransform(scrollY, [0, 700], [1, 0.1]);

  const scale1 = useTransform(scrollY, [0, 1000], [1, prefersReducedMotion ? 1 : 1.2]);
  const scale2 = useTransform(scrollY, [0, 1000], [1, prefersReducedMotion ? 1 : 1.1]);

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Background layer 1 - Slowest, largest elements */}
      <motion.div
        className="absolute inset-0"
        style={{ y: y1, opacity: opacity1, scale: scale1 }}
      >
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-br from-primary/10 via-accent/5 to-transparent rounded-full blur-3xl" />
        <div className="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-accent/10 via-secondary/5 to-transparent rounded-full blur-2xl" />
        <div className="absolute bottom-20 left-1/3 w-64 h-64 bg-gradient-to-br from-secondary/10 via-primary/5 to-transparent rounded-full blur-3xl" />
      </motion.div>

      {/* Background layer 2 - Medium speed, medium elements */}
      <motion.div
        className="absolute inset-0"
        style={{ y: y2, opacity: opacity2, scale: scale2 }}
      >
        <div className="absolute top-60 left-1/4 w-48 h-48 bg-gradient-to-br from-accent/15 via-primary/8 to-transparent rounded-full blur-2xl" />
        <div className="absolute bottom-40 right-1/3 w-56 h-56 bg-gradient-to-br from-primary/15 via-secondary/8 to-transparent rounded-full blur-xl" />
        <div className="absolute top-1/3 right-10 w-40 h-40 bg-gradient-to-br from-secondary/15 via-accent/8 to-transparent rounded-full blur-2xl" />
      </motion.div>

      {/* Background layer 3 - Fastest, smallest elements */}
      <motion.div
        className="absolute inset-0"
        style={{ y: y3, opacity: opacity3 }}
      >
        <div className="absolute top-32 right-1/4 w-24 h-24 bg-primary/20 rounded-full blur-xl" />
        <div className="absolute bottom-32 left-1/4 w-32 h-32 bg-accent/20 rounded-full blur-lg" />
        <div className="absolute top-2/3 left-10 w-20 h-20 bg-secondary/20 rounded-full blur-xl" />
        <div className="absolute bottom-1/4 right-20 w-28 h-28 bg-primary/15 rounded-full blur-lg" />
      </motion.div>

      {/* Animated grid pattern with parallax */}
      <motion.div
        className="absolute inset-0 opacity-[0.02]"
        style={{ y: y4 }}
      >
        <div 
          className="w-full h-full"
          style={{
            backgroundImage: `
              radial-gradient(circle at 1px 1px, ${
                theme === 'dark' ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.2)'
              } 1px, transparent 0),
              linear-gradient(90deg, ${
                theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              } 1px, transparent 1px),
              linear-gradient(${
                theme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)'
              } 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px, 50px 50px, 50px 50px',
          }}
        />
      </motion.div>

      {/* Floating geometric shapes */}
      <motion.div
        className="absolute inset-0"
        style={{ y: y2 }}
      >
        {/* Top left floating shape */}
        <motion.div
          className="absolute top-1/4 left-1/6 w-4 h-4 border border-primary/30"
          style={{
            clipPath: 'polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)',
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear',
          }}
        />

        {/* Top right floating shape */}
        <motion.div
          className="absolute top-1/3 right-1/4 w-6 h-6 border border-accent/30 rounded-full"
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />

        {/* Bottom floating shapes */}
        <motion.div
          className="absolute bottom-1/3 left-1/3 w-3 h-3 bg-secondary/30 rounded-sm"
          animate={{
            rotate: [0, 45, 0],
            scale: [1, 1.5, 1],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
      </motion.div>

      {/* Subtle light rays effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none"
        style={{ opacity: opacity1 }}
      >
        <div 
          className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-primary/10 to-transparent transform -skew-x-12"
        />
        <div 
          className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-accent/10 to-transparent transform skew-x-12"
        />
      </motion.div>

      {children}
    </div>
  );
}
