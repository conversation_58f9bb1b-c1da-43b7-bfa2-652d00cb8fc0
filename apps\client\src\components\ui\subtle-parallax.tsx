'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { useReducedMotion } from '@/hooks/use-reduced-motion';

interface SubtleParallaxProps {
  children: React.ReactNode;
  className?: string;
  intensity?: number;
  direction?: 'up' | 'down';
}

export function SubtleParallax({ 
  children, 
  className = '', 
  intensity = 0.3,
  direction = 'up' 
}: SubtleParallaxProps) {
  const ref = useRef<HTMLDivElement>(null);
  const prefersReducedMotion = useReducedMotion();
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  // Subtle parallax movement
  const y = useTransform(
    scrollYProgress, 
    [0, 1], 
    direction === 'up' 
      ? [0, prefersReducedMotion ? 0 : -50 * intensity]
      : [0, prefersReducedMotion ? 0 : 50 * intensity]
  );

  // Very subtle opacity change
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0.8, 1, 1, 0.9]);

  return (
    <motion.div
      ref={ref}
      className={className}
      style={{ y, opacity }}
    >
      {children}
    </motion.div>
  );
}

// Component for background elements with parallax
export function ParallaxBackground({ 
  children, 
  className = '',
  speed = 0.5 
}: { 
  children: React.ReactNode; 
  className?: string;
  speed?: number;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const prefersReducedMotion = useReducedMotion();
  
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });

  const y = useTransform(
    scrollYProgress, 
    [0, 1], 
    [0, prefersReducedMotion ? 0 : -100 * speed]
  );

  return (
    <motion.div
      ref={ref}
      className={`absolute inset-0 ${className}`}
      style={{ y }}
    >
      {children}
    </motion.div>
  );
}

// Component for floating elements
export function FloatingElement({ 
  children, 
  className = '',
  delay = 0,
  amplitude = 10,
  duration = 4 
}: { 
  children: React.ReactNode; 
  className?: string;
  delay?: number;
  amplitude?: number;
  duration?: number;
}) {
  const prefersReducedMotion = useReducedMotion();

  if (prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  return (
    <motion.div
      className={className}
      animate={{
        y: [-amplitude, amplitude, -amplitude],
        rotate: [-2, 2, -2],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeInOut",
        delay,
      }}
    >
      {children}
    </motion.div>
  );
}
