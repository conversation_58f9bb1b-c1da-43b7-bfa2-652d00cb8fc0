import { Router } from 'express';
import { BlogPost, BlogCategory } from '@rxy/database';
import { validateBody, validateQuery } from '@/middleware/validation';
import { authenticate, authorize } from '@/middleware/auth';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { 
  schemas, 
  HTTP_STATUS, 
  SUCCESS_MESSAGES, 
  ERROR_MESSAGES 
} from '@rxy/shared';

const router = Router();

// Get all published blog posts with pagination and filtering
router.get('/posts', 
  validateQuery(schemas.pagination),
  asyncHandler(async (req: any, res: any) => {
    const { 
      page = 1, 
      limit = 10, 
      category, 
      tag, 
      search,
      sort = 'publishedAt'
    } = req.query;

    const skip = (page - 1) * limit;
    let query: any = { status: 'published' };

    // Add filters
    if (category) {
      query.category = category;
    }
    if (tag) {
      query.tags = tag;
    }
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { tags: { $regex: search, $options: 'i' } },
      ];
    }

    // Sort options
    let sortOption: any = {};
    switch (sort) {
      case 'publishedAt':
        sortOption = { publishedAt: -1 };
        break;
      case 'views':
        sortOption = { views: -1 };
        break;
      case 'likes':
        sortOption = { likes: -1 };
        break;
      case 'title':
        sortOption = { title: 1 };
        break;
      default:
        sortOption = { publishedAt: -1 };
    }

    const [posts, total] = await Promise.all([
      BlogPost.find(query)
        .sort(sortOption)
        .skip(skip)
        .limit(parseInt(limit))
        .populate('author', 'name avatar')
        .lean(),
      BlogPost.countDocuments(query)
    ]);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });
  })
);

// Get single blog post by slug
router.get('/posts/:slug', 
  asyncHandler(async (req: any, res: any) => {
    const { slug } = req.params;

    const post = await BlogPost.findBySlug(slug)
      .populate('author', 'name avatar');

    if (!post || post.status !== 'published') {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Increment views
    await post.incrementViews();

    res.json({
      success: true,
      data: { post },
    });
  })
);

// Create new blog post (admin only)
router.post('/posts',
  authenticate,
  authorize(['admin']),
  validateBody(schemas.blogPost),
  asyncHandler(async (req: any, res: any) => {
    const postData = {
      ...req.body,
      author: req.user.id,
    };

    const post = new BlogPost(postData);
    await post.save();

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: SUCCESS_MESSAGES.CREATED,
      data: { post },
    });
  })
);

// Update blog post (admin only)
router.put('/posts/:id',
  authenticate,
  authorize(['admin']),
  validateBody(schemas.blogPost),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    const post = await BlogPost.findById(id);
    if (!post) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    Object.assign(post, req.body);
    await post.save();

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATED,
      data: { post },
    });
  })
);

// Delete blog post (admin only)
router.delete('/posts/:id',
  authenticate,
  authorize(['admin']),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    const post = await BlogPost.findById(id);
    if (!post) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    await BlogPost.findByIdAndDelete(id);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.DELETED,
    });
  })
);

// Get all blog categories
router.get('/categories',
  asyncHandler(async (req: any, res: any) => {
    const categories = await BlogCategory.find().sort({ name: 1 });

    res.json({
      success: true,
      data: { categories },
    });
  })
);

// Create blog category (admin only)
router.post('/categories',
  authenticate,
  authorize(['admin']),
  validateBody(schemas.blogCategory),
  asyncHandler(async (req: any, res: any) => {
    const category = new BlogCategory(req.body);
    await category.save();

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: SUCCESS_MESSAGES.CREATED,
      data: { category },
    });
  })
);

// Get popular posts
router.get('/posts/popular',
  asyncHandler(async (req: any, res: any) => {
    const { limit = 5 } = req.query;

    const posts = await BlogPost.find({ status: 'published' })
      .sort({ views: -1, likes: -1 })
      .limit(parseInt(limit))
      .populate('author', 'name avatar')
      .lean();

    res.json({
      success: true,
      data: { posts },
    });
  })
);

// Get recent posts
router.get('/posts/recent',
  asyncHandler(async (req: any, res: any) => {
    const { limit = 5 } = req.query;

    const posts = await BlogPost.find({ status: 'published' })
      .sort({ publishedAt: -1 })
      .limit(parseInt(limit))
      .populate('author', 'name avatar')
      .lean();

    res.json({
      success: true,
      data: { posts },
    });
  })
);

// Like a blog post
router.post('/posts/:slug/like',
  asyncHandler(async (req: any, res: any) => {
    const { slug } = req.params;

    const post = await BlogPost.findBySlug(slug);
    if (!post || post.status !== 'published') {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    await post.incrementLikes();

    res.json({
      success: true,
      message: 'Post liked successfully',
      data: { likes: post.likes },
    });
  })
);

export default router;
