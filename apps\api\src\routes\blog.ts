import { Router } from 'express';
import { BlogPost, BlogCategory, isDatabaseConnected } from '@rxy/database';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { MockBlogService } from '@/services/mockBlogService';
import { HTTP_STATUS, ERROR_MESSAGES } from '@rxy/shared';

const router = Router();

// Get all published blog posts with pagination and filtering
router.get(
  '/posts',
  asyncHandler(async (req: any, res: any) => {
    const { page = 1, limit = 10, category, tag, search, sort = 'publishedAt' } = req.query;

    let result;

    if (isDatabaseConnected()) {
      // Use database
      const skip = (page - 1) * limit;
      let query: any = { status: 'published' };

      if (category) query.category = category;
      if (tag) query.tags = tag;
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { content: { $regex: search, $options: 'i' } },
          { excerpt: { $regex: search, $options: 'i' } },
          { tags: { $regex: search, $options: 'i' } },
        ];
      }

      let sortOption: any = {};
      switch (sort) {
        case 'views':
          sortOption = { views: -1 };
          break;
        case 'likes':
          sortOption = { likes: -1 };
          break;
        case 'title':
          sortOption = { title: 1 };
          break;
        default:
          sortOption = { publishedAt: -1 };
      }

      const [posts, total] = await Promise.all([
        BlogPost.find(query)
          .sort(sortOption)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('author', 'name avatar')
          .lean(),
        BlogPost.countDocuments(query),
      ]);

      const totalPages = Math.ceil(total / limit);
      result = {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } else {
      // Use mock service
      const mockResult = await MockBlogService.getPosts({
        page,
        limit,
        category,
        tag,
        search,
        sort,
      });

      result = {
        posts: mockResult.posts,
        pagination: {
          page: mockResult.page,
          limit: mockResult.limit,
          total: mockResult.total,
          totalPages: mockResult.totalPages,
          hasNext: mockResult.page < mockResult.totalPages,
          hasPrev: mockResult.page > 1,
        },
      };
    }

    res.json({
      success: true,
      data: result,
    });
  })
);

// Get single blog post by slug
router.get(
  '/posts/:slug',
  asyncHandler(async (req: any, res: any) => {
    const { slug } = req.params;
    let post;

    if (isDatabaseConnected()) {
      post = await BlogPost.findOne({ slug, status: 'published' }).populate(
        'author',
        'name avatar'
      );

      if (post) {
        post.views += 1;
        await post.save();
      }
    } else {
      post = await MockBlogService.getPostBySlug(slug);
    }

    if (!post) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    res.json({
      success: true,
      data: { post },
    });
  })
);

// Get all blog categories
router.get(
  '/categories',
  asyncHandler(async (req: any, res: any) => {
    let categories;

    if (isDatabaseConnected()) {
      categories = await BlogCategory.find().sort({ name: 1 });
    } else {
      categories = await MockBlogService.getCategories();
    }

    res.json({
      success: true,
      data: { categories },
    });
  })
);

// Get popular posts
router.get(
  '/popular',
  asyncHandler(async (req: any, res: any) => {
    const { limit = 5 } = req.query;
    let posts;

    if (isDatabaseConnected()) {
      posts = await BlogPost.find({ status: 'published' })
        .sort({ views: -1, likes: -1 })
        .limit(parseInt(limit))
        .populate('author', 'name avatar')
        .lean();
    } else {
      const result = await MockBlogService.getPosts({ limit, sort: 'views' });
      posts = result.posts;
    }

    res.json({
      success: true,
      data: { posts },
    });
  })
);

// Get recent posts
router.get(
  '/recent',
  asyncHandler(async (req: any, res: any) => {
    const { limit = 5 } = req.query;
    let posts;

    if (isDatabaseConnected()) {
      posts = await BlogPost.find({ status: 'published' })
        .sort({ publishedAt: -1 })
        .limit(parseInt(limit))
        .populate('author', 'name avatar')
        .lean();
    } else {
      const result = await MockBlogService.getPosts({ limit, sort: 'publishedAt' });
      posts = result.posts;
    }

    res.json({
      success: true,
      data: { posts },
    });
  })
);

// Like a blog post
router.post(
  '/posts/:slug/like',
  asyncHandler(async (req: any, res: any) => {
    const { slug } = req.params;
    let result;

    if (isDatabaseConnected()) {
      const post = await BlogPost.findOne({ slug, status: 'published' });
      if (!post) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }

      post.likes += 1;
      await post.save();
      result = { likes: post.likes };
    } else {
      result = await MockBlogService.likePost(slug);
      if (!result) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }
    }

    res.json({
      success: true,
      message: 'Post liked successfully',
      data: result,
    });
  })
);

export default router;
