'use client';

import { useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useSpring } from 'framer-motion';
import { ArrowDown, Play, Code, Zap, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { OptimizedShootingStars } from '@/components/ui/shooting-star';
import { FloatingGeometry } from '@/components/ui/floating-geometry';
import { ParallaxBackground } from '@/components/ui/parallax-background';
import { TypewriterText } from '@/components/ui/typewriter-text';
import { useMousePosition } from '@/hooks/use-mouse-position';
import { useReducedMotion } from '@/hooks/use-reduced-motion';
import { siteConfig } from '@/config/site';

const stats = [
  { icon: Code, label: 'Projects Delivered', value: siteConfig.business.projectsCompleted },
  { icon: Zap, label: 'Years Experience', value: siteConfig.business.experience },
  { icon: Users, label: 'Happy Clients', value: siteConfig.business.happyClients },
];

const techStack = siteConfig.techStack.frontend.slice(0, 6);

// Dynamic text for typewriter effect - Starting with Software Engineer
const typewriterTexts = [
  'Software Engineer',
  'Fullstack Developer',
  'Code Architect',
  'Tech Innovator',
  'Problem Solver',
];

export function HeroSection() {
  const [mounted, setMounted] = useState(false);
  const { scrollY } = useScroll();
  const mousePosition = useMousePosition();
  const prefersReducedMotion = useReducedMotion();

  // Enhanced parallax effects with spring physics
  const y = useSpring(useTransform(scrollY, [0, 500], [0, prefersReducedMotion ? 50 : 150]), {
    stiffness: 100,
    damping: 30,
  });
  const opacity = useTransform(scrollY, [0, 300], [1, 0]);
  const yReverse = useSpring(
    useTransform(scrollY, [0, 500], [0, prefersReducedMotion ? -30 : -100]),
    {
      stiffness: 100,
      damping: 30,
    }
  );

  // Mouse parallax for interactive elements
  const mouseParallaxX =
    (mousePosition.x - (typeof window !== 'undefined' ? window.innerWidth / 2 : 0)) * 0.02;
  const mouseParallaxY =
    (mousePosition.y - (typeof window !== 'undefined' ? window.innerHeight / 2 : 0)) * 0.02;

  useEffect(() => {
    setMounted(true);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.querySelector(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Use opacity instead of conditional rendering to avoid hook order issues
  if (!mounted) {
    return (
      <section
        id="home"
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-muted/20"
        style={{ opacity: 0 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">Loading...</div>
        </div>
      </section>
    );
  }

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-muted/20"
    >
      {/* Enhanced Parallax Background */}
      <ParallaxBackground>
        {/* Shooting Stars Animation */}
        <OptimizedShootingStars />

        {/* 3D Floating Geometry - Main */}
        <motion.div
          className="absolute top-1/4 right-1/6 z-10"
          style={{
            x: mouseParallaxX,
            y: mouseParallaxY,
          }}
        >
          <FloatingGeometry size="lg" intensity={1.2} />
        </motion.div>

        {/* Secondary floating geometry */}
        <motion.div
          className="absolute bottom-1/3 left-1/8 z-5"
          style={{
            x: mouseParallaxX * -0.5,
            y: mouseParallaxY * -0.5,
          }}
        >
          <FloatingGeometry size="sm" intensity={0.8} />
        </motion.div>

        {/* Tertiary floating geometry */}
        <motion.div
          className="absolute top-2/3 right-1/3 z-5"
          style={{
            x: mouseParallaxX * 0.3,
            y: mouseParallaxY * 0.3,
          }}
        >
          <FloatingGeometry size="md" intensity={0.6} />
        </motion.div>
      </ParallaxBackground>

      {/* Status Badge - Top Right - Fixed positioning to avoid header overlap */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="absolute top-24 lg:top-28 right-4 sm:right-8 z-[90]"
      >
        <Badge
          variant="secondary"
          className="px-3 py-1.5 text-xs font-medium bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800 shadow-lg backdrop-blur-sm"
        >
          <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
          {siteConfig.business.status}
        </Badge>
      </motion.div>

      {/* Main Content - Enhanced responsive constraints */}
      <div className="relative z-10 max-w-[1200px] xl:max-w-[1400px] 2xl:max-w-[1600px] mx-auto px-4 sm:px-6 lg:px-8 xl:px-12 text-center py-20">
        <motion.div
          style={{ opacity }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-12"
        >
          {/* Enhanced Main Headline with Typewriter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="space-y-6"
            style={{
              x: mouseParallaxX * 0.5,
              y: mouseParallaxY * 0.5,
            }}
          >
            <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold tracking-tight">
              <motion.span
                className="block text-foreground mb-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                <TypewriterText
                  texts={typewriterTexts}
                  className="bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent"
                  speed={120}
                  deleteSpeed={80}
                  delayBetween={3000}
                />
              </motion.span>
              <motion.span
                className="block text-muted-foreground text-2xl sm:text-3xl lg:text-4xl font-medium"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                Building the Future with Code
              </motion.span>
            </h1>

            <motion.p
              className="max-w-3xl mx-auto text-lg sm:text-xl lg:text-2xl text-muted-foreground leading-relaxed"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              {siteConfig.tagline}
            </motion.p>
          </motion.div>

          {/* Tech Stack */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="flex flex-wrap justify-center gap-3 max-w-2xl mx-auto"
          >
            {techStack.map((tech, index) => (
              <motion.span
                key={tech}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="px-3 py-1 text-sm font-medium bg-muted/50 text-muted-foreground rounded-full border border-border/50 hover:bg-muted transition-colors"
              >
                {tech}
              </motion.span>
            ))}
          </motion.div>

          {/* Enhanced CTA Buttons with Micro-interactions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
            >
              <Button
                size="lg"
                onClick={() => scrollToSection('#projects')}
                className="group px-8 py-4 text-lg font-semibold bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground shadow-lg hover:shadow-2xl transition-all duration-300 relative overflow-hidden"
              >
                <span className="relative z-10">View My Work</span>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-accent to-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={false}
                />
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
            >
              <Button
                variant="outline"
                size="lg"
                onClick={() => scrollToSection('#contact')}
                className="group px-8 py-4 text-lg font-semibold border-2 border-primary/30 hover:border-primary hover:bg-primary/10 transition-all duration-300 backdrop-blur-sm"
              >
                <motion.div
                  className="flex items-center"
                  whileHover={{ x: 5 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                >
                  <Play className="w-5 h-5 mr-2 group-hover:text-primary transition-colors" />
                  Let's Talk
                </motion.div>
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
          className="mt-20 grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 + index * 0.1 }}
              className="text-center group"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 mb-4 bg-primary/10 rounded-2xl group-hover:bg-primary/20 transition-colors duration-300">
                <stat.icon className="w-8 h-8 text-primary" />
              </div>
              <div className="text-3xl font-bold text-foreground mb-1">{stat.value}</div>
              <div className="text-sm text-muted-foreground font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Enhanced Scroll Indicator - Positioned to avoid header overlap */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1.4 }}
        className="absolute bottom-8 right-4 sm:right-8 z-[80]"
        style={{
          x: mouseParallaxX * -0.3,
          y: mouseParallaxY * -0.3,
        }}
      >
        <motion.button
          onClick={() => scrollToSection('#about')}
          className="group flex items-center text-muted-foreground hover:text-foreground transition-all duration-300 p-4 rounded-2xl bg-background/60 backdrop-blur-md border border-border/30 hover:border-primary/50 hover:bg-background/80 shadow-lg hover:shadow-xl"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          animate={{
            y: prefersReducedMotion ? 0 : [0, 8, 0],
          }}
          transition={{
            y: { duration: 3, repeat: Infinity, ease: 'easeInOut' },
            scale: { type: 'spring', stiffness: 400, damping: 17 },
          }}
        >
          <motion.span
            className="text-sm font-medium mr-3 group-hover:text-primary transition-colors hidden sm:block"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 1.6 }}
          >
            Explore More
          </motion.span>
          <motion.div
            animate={{
              rotate: prefersReducedMotion ? 0 : [0, 5, -5, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: 0.5,
            }}
          >
            <ArrowDown className="w-5 h-5 group-hover:text-primary transition-colors" />
          </motion.div>
        </motion.button>
      </motion.div>
    </section>
  );
}
