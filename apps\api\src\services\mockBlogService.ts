// Mock blog service for development without database
export const mockBlogPosts = [
  {
    id: '1',
    title: 'Building Scalable React Applications: Best Practices for 2024',
    slug: 'building-scalable-react-applications-2024',
    content: `# Building Scalable React Applications: Best Practices for 2024

React has evolved significantly over the years, and building scalable applications requires understanding modern patterns and best practices. In this comprehensive guide, we'll explore the essential techniques for creating maintainable React applications.

## Component Architecture

The foundation of any scalable React application lies in its component architecture. Here are the key principles:

### 1. Single Responsibility Principle
Each component should have a single, well-defined purpose. This makes components easier to test, maintain, and reuse.

### 2. Composition over Inheritance
React favors composition patterns. Instead of creating complex inheritance hierarchies, compose smaller components to build larger ones.

### 3. Container and Presentational Components
Separate your components into containers (logic) and presentational (UI) components for better separation of concerns.

## State Management

Modern React applications benefit from thoughtful state management strategies:

### Local State with useState and useReducer
For component-specific state, React's built-in hooks are often sufficient.

### Context API for Shared State
Use React Context for state that needs to be shared across multiple components without prop drilling.

### External State Management
For complex applications, consider libraries like Redux Toolkit, Zustand, or Jotai.

## Performance Optimization

### React.memo and useMemo
Prevent unnecessary re-renders by memoizing components and expensive calculations.

### Code Splitting
Use React.lazy() and Suspense to split your code and load components on demand.

### Virtual Scrolling
For large lists, implement virtual scrolling to maintain performance.

## Testing Strategies

### Unit Testing
Test individual components in isolation using React Testing Library.

### Integration Testing
Test how components work together and interact with external services.

### End-to-End Testing
Use tools like Cypress or Playwright for full user journey testing.

## Conclusion

Building scalable React applications requires careful planning, adherence to best practices, and continuous refactoring. By following these guidelines, you'll create applications that can grow with your business needs.`,
    excerpt: 'Discover the latest patterns and techniques for building maintainable React applications that scale with your business needs.',
    featuredImage: '/images/blog/react-scalability.jpg',
    tags: ['React', 'JavaScript', 'Performance', 'Architecture'],
    category: 'React',
    status: 'published' as const,
    seo: {
      metaTitle: 'Building Scalable React Applications: Best Practices for 2024',
      metaDescription: 'Learn essential techniques for creating maintainable React applications that scale.',
      keywords: ['React', 'Scalability', 'Best Practices', 'Architecture'],
    },
    author: 'admin-user-id',
    publishedAt: new Date('2024-01-15'),
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
    views: 12500,
    likes: 245,
  },
  {
    id: '2',
    title: 'The Future of AI in Web Development: Trends and Predictions',
    slug: 'future-ai-web-development-trends',
    content: `# The Future of AI in Web Development: Trends and Predictions

Artificial Intelligence is revolutionizing web development, from automated code generation to intelligent user experiences. Let's explore what the future holds.

## Current AI Applications in Web Development

### Code Generation and Assistance
Tools like GitHub Copilot and ChatGPT are already helping developers write code faster and more efficiently.

### Automated Testing
AI-powered testing tools can generate test cases and identify potential bugs automatically.

### Design Automation
AI can generate layouts, color schemes, and even entire design systems based on requirements.

## Emerging Trends

### 1. Intelligent User Interfaces
AI will enable more personalized and adaptive user interfaces that learn from user behavior.

### 2. Automated Accessibility
AI tools will automatically ensure web applications meet accessibility standards.

### 3. Performance Optimization
Machine learning algorithms will optimize application performance in real-time.

## Predictions for the Next 5 Years

The integration of AI in web development will become even more seamless, with tools that can:
- Generate entire applications from natural language descriptions
- Automatically optimize code for performance and security
- Provide real-time suggestions for improving user experience

## Preparing for the AI-Driven Future

As developers, we need to:
- Embrace AI tools while maintaining our problem-solving skills
- Focus on higher-level architecture and design decisions
- Develop skills in prompt engineering and AI collaboration

The future of web development is exciting, and AI will be a powerful ally in creating better digital experiences.`,
    excerpt: 'Explore how artificial intelligence is transforming web development and what trends to expect in the coming years.',
    featuredImage: '/images/blog/ai-web-development.jpg',
    tags: ['AI', 'Machine Learning', 'Web Development', 'Future Tech'],
    category: 'AI/ML',
    status: 'published' as const,
    seo: {
      metaTitle: 'The Future of AI in Web Development: Trends and Predictions',
      metaDescription: 'Discover how AI is transforming web development and future trends.',
      keywords: ['AI', 'Web Development', 'Machine Learning', 'Future'],
    },
    author: 'admin-user-id',
    publishedAt: new Date('2024-01-10'),
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10'),
    views: 8900,
    likes: 189,
  },
  {
    id: '3',
    title: 'Microservices Architecture: When and How to Implement',
    slug: 'microservices-architecture-guide',
    content: `# Microservices Architecture: When and How to Implement

Microservices architecture has gained popularity for building scalable, maintainable applications. But when should you use it, and how do you implement it effectively?

## What are Microservices?

Microservices are a software architecture pattern where applications are built as a collection of small, independent services that communicate over well-defined APIs.

## Benefits of Microservices

### Scalability
Each service can be scaled independently based on demand.

### Technology Diversity
Different services can use different technologies and programming languages.

### Team Independence
Teams can work on different services independently, improving development velocity.

### Fault Isolation
If one service fails, it doesn't necessarily bring down the entire application.

## When to Use Microservices

### Large, Complex Applications
Microservices work best for large applications with multiple business domains.

### Multiple Teams
When you have multiple development teams that need to work independently.

### Different Scaling Requirements
When different parts of your application have different scaling needs.

## Implementation Strategies

### Start with a Monolith
Begin with a monolithic architecture and extract services as you identify clear boundaries.

### Domain-Driven Design
Use domain-driven design principles to identify service boundaries.

### API-First Approach
Design your APIs before implementing the services.

### Infrastructure Automation
Invest in containerization, orchestration, and CI/CD pipelines.

## Challenges and Solutions

### Service Communication
Use asynchronous messaging and event-driven architectures where possible.

### Data Management
Each service should own its data, but you'll need strategies for cross-service queries.

### Monitoring and Observability
Implement comprehensive logging, metrics, and distributed tracing.

### Testing
Develop strategies for testing individual services and their interactions.

## Conclusion

Microservices can provide significant benefits for the right use cases, but they also introduce complexity. Carefully consider your requirements and team capabilities before making the transition.`,
    excerpt: 'A comprehensive guide to understanding microservices architecture, its benefits, challenges, and implementation strategies.',
    featuredImage: '/images/blog/microservices.jpg',
    tags: ['Microservices', 'Backend', 'Architecture', 'DevOps'],
    category: 'Architecture',
    status: 'published' as const,
    seo: {
      metaTitle: 'Microservices Architecture: When and How to Implement',
      metaDescription: 'Learn when and how to implement microservices architecture effectively.',
      keywords: ['Microservices', 'Architecture', 'Backend', 'Scalability'],
    },
    author: 'admin-user-id',
    publishedAt: new Date('2024-01-05'),
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
    views: 15200,
    likes: 312,
  },
];

export const mockCategories = [
  {
    id: '1',
    name: 'React',
    slug: 'react',
    description: 'Articles about React development, best practices, and advanced patterns.',
    color: '#61DAFB',
  },
  {
    id: '2',
    name: 'AI/ML',
    slug: 'ai-ml',
    description: 'Artificial Intelligence and Machine Learning in web development.',
    color: '#FF6B6B',
  },
  {
    id: '3',
    name: 'Architecture',
    slug: 'architecture',
    description: 'Software architecture patterns and system design.',
    color: '#4ECDC4',
  },
  {
    id: '4',
    name: 'TypeScript',
    slug: 'typescript',
    description: 'TypeScript tips, tricks, and best practices.',
    color: '#3178C6',
  },
  {
    id: '5',
    name: 'DevOps',
    slug: 'devops',
    description: 'DevOps practices, tools, and automation.',
    color: '#FFA726',
  },
];

export class MockBlogService {
  static async getPosts(filters: any = {}) {
    let posts = [...mockBlogPosts];
    
    // Apply filters
    if (filters.category) {
      posts = posts.filter(post => post.category === filters.category);
    }
    
    if (filters.tag) {
      posts = posts.filter(post => post.tags.includes(filters.tag));
    }
    
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      posts = posts.filter(post => 
        post.title.toLowerCase().includes(searchTerm) ||
        post.content.toLowerCase().includes(searchTerm) ||
        post.excerpt.toLowerCase().includes(searchTerm) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }
    
    // Apply sorting
    switch (filters.sort) {
      case 'views':
        posts.sort((a, b) => b.views - a.views);
        break;
      case 'likes':
        posts.sort((a, b) => b.likes - a.likes);
        break;
      case 'title':
        posts.sort((a, b) => a.title.localeCompare(b.title));
        break;
      default:
        posts.sort((a, b) => b.publishedAt.getTime() - a.publishedAt.getTime());
    }
    
    // Apply pagination
    const page = parseInt(filters.page) || 1;
    const limit = parseInt(filters.limit) || 10;
    const skip = (page - 1) * limit;
    const paginatedPosts = posts.slice(skip, skip + limit);
    
    return {
      posts: paginatedPosts,
      total: posts.length,
      page,
      limit,
      totalPages: Math.ceil(posts.length / limit),
    };
  }
  
  static async getPostBySlug(slug: string) {
    const post = mockBlogPosts.find(p => p.slug === slug);
    if (post) {
      // Simulate view increment
      post.views += 1;
    }
    return post;
  }
  
  static async getCategories() {
    return mockCategories;
  }
  
  static async likePost(slug: string) {
    const post = mockBlogPosts.find(p => p.slug === slug);
    if (post) {
      post.likes += 1;
      return { likes: post.likes };
    }
    return null;
  }
}
