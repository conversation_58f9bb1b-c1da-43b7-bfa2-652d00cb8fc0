import { getSession } from 'next-auth/react';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

class AdminApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const session = await getSession();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (session?.accessToken) {
      headers.Authorization = `Bearer ${session.accessToken}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = await this.getAuthHeaders();
    
    const config: RequestInit = {
      headers: {
        ...headers,
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Blog Management
  async getBlogPosts(params?: {
    page?: number;
    limit?: number;
    status?: 'draft' | 'published' | 'archived';
    category?: string;
    search?: string;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/api/admin/blog/posts${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  async createBlogPost(data: any): Promise<ApiResponse<any>> {
    return this.request('/api/admin/blog/posts', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateBlogPost(id: string, data: any): Promise<ApiResponse<any>> {
    return this.request(`/api/admin/blog/posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteBlogPost(id: string): Promise<ApiResponse<any>> {
    return this.request(`/api/admin/blog/posts/${id}`, {
      method: 'DELETE',
    });
  }

  async getBlogCategories(): Promise<ApiResponse<any>> {
    return this.request('/api/admin/blog/categories');
  }

  async createBlogCategory(data: any): Promise<ApiResponse<any>> {
    return this.request('/api/admin/blog/categories', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Analytics
  async getAnalytics(params?: {
    startDate?: string;
    endDate?: string;
    metric?: string;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/api/admin/analytics${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  // Messages
  async getMessages(params?: {
    page?: number;
    limit?: number;
    status?: 'unread' | 'read' | 'replied' | 'archived';
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/api/admin/messages${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  async updateMessageStatus(id: string, status: string): Promise<ApiResponse<any>> {
    return this.request(`/api/admin/messages/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async replyToMessage(id: string, reply: string): Promise<ApiResponse<any>> {
    return this.request(`/api/admin/messages/${id}/reply`, {
      method: 'POST',
      body: JSON.stringify({ reply }),
    });
  }

  // Media Management
  async uploadMedia(file: File, type: string): Promise<ApiResponse<any>> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const session = await getSession();
    const headers: Record<string, string> = {};

    if (session?.accessToken) {
      headers.Authorization = `Bearer ${session.accessToken}`;
    }

    return fetch(`${this.baseUrl}/api/admin/media/upload`, {
      method: 'POST',
      headers,
      body: formData,
    }).then(res => res.json());
  }

  async getMedia(params?: {
    page?: number;
    limit?: number;
    type?: string;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/api/admin/media${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  // Newsletter Management
  async getNewsletterSubscribers(params?: {
    page?: number;
    limit?: number;
    status?: 'active' | 'unsubscribed';
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/api/admin/newsletter/subscribers${queryString ? `?${queryString}` : ''}`;
    
    return this.request(endpoint);
  }

  async createNewsletterCampaign(data: any): Promise<ApiResponse<any>> {
    return this.request('/api/admin/newsletter/campaigns', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Site Settings
  async getSiteSettings(): Promise<ApiResponse<any>> {
    return this.request('/api/admin/settings');
  }

  async updateSiteSettings(data: any): Promise<ApiResponse<any>> {
    return this.request('/api/admin/settings', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }
}

// Create singleton instance
export const adminApi = new AdminApiClient();

// Export types for use in components
export type { ApiResponse };
