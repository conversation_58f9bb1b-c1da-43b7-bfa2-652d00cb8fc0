import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { BlogPostPage } from '@/components/pages/blog-post-page';
import { siteConfig } from '@/config/site';
import { blogApi } from '@/lib/api';

// Mock data removed - now using API
  {
    id: 1,
    title: 'Building Scalable React Applications: Best Practices for 2024',
    excerpt:
      'Discover the latest patterns and techniques for building maintainable React applications that scale with your business needs.',
    content: `
# Building Scalable React Applications: Best Practices for 2024

React has evolved significantly over the years, and with it, the patterns and practices for building scalable applications. In this comprehensive guide, we'll explore the latest techniques and best practices that will help you build maintainable React applications that can grow with your business needs.

## Component Architecture

### 1. Composition over Inheritance

React favors composition over inheritance, and this principle becomes even more important when building scalable applications. Instead of creating complex inheritance hierarchies, focus on composing smaller, reusable components.

\`\`\`jsx
// Good: Composition
function UserProfile({ user, actions }) {
  return (
    <div className="user-profile">
      <UserAvatar user={user} />
      <UserInfo user={user} />
      <UserActions actions={actions} />
    </div>
  );
}

// Better: Even more granular composition
function UserProfile({ user, actions }) {
  return (
    <Card>
      <CardHeader>
        <UserAvatar user={user} />
        <UserBasicInfo user={user} />
      </CardHeader>
      <CardContent>
        <UserDetailedInfo user={user} />
      </CardContent>
      <CardFooter>
        <UserActions actions={actions} />
      </CardFooter>
    </Card>
  );
}
\`\`\`

### 2. Custom Hooks for Logic Reuse

Custom hooks are one of React's most powerful features for code reuse and separation of concerns.

\`\`\`jsx
// Custom hook for user data management
function useUser(userId) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchUser() {
      try {
        setLoading(true);
        const userData = await api.getUser(userId);
        setUser(userData);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    }

    fetchUser();
  }, [userId]);

  return { user, loading, error };
}
\`\`\`

## State Management

### 1. Choose the Right Tool

Not every application needs Redux or Zustand. Start with React's built-in state management and scale up as needed:

- **useState + useContext**: For simple global state
- **useReducer**: For complex state logic
- **Zustand/Redux Toolkit**: For complex applications with multiple data sources
- **React Query/SWR**: For server state management

### 2. Separate Client and Server State

One of the biggest improvements you can make to your React application is properly separating client state from server state.

\`\`\`jsx
// Client state: UI-specific state
const [isModalOpen, setIsModalOpen] = useState(false);
const [selectedTab, setSelectedTab] = useState('overview');

// Server state: Data from your API
const { data: users, isLoading, error } = useQuery('users', fetchUsers);
\`\`\`

## Performance Optimization

### 1. Lazy Loading and Code Splitting

Implement route-based code splitting to reduce initial bundle size:

\`\`\`jsx
import { lazy, Suspense } from 'react';

const Dashboard = lazy(() => import('./pages/Dashboard'));
const Profile = lazy(() => import('./pages/Profile'));

function App() {
  return (
    <Router>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/profile" element={<Profile />} />
        </Routes>
      </Suspense>
    </Router>
  );
}
\`\`\`

### 2. Memoization Strategies

Use React.memo, useMemo, and useCallback strategically:

\`\`\`jsx
// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Memoize callback functions
const handleClick = useCallback((id) => {
  onItemClick(id);
}, [onItemClick]);

// Memoize components
const UserCard = React.memo(({ user, onEdit }) => {
  return (
    <div>
      <h3>{user.name}</h3>
      <button onClick={() => onEdit(user.id)}>Edit</button>
    </div>
  );
});
\`\`\`

## Testing Strategies

### 1. Testing Pyramid

Follow the testing pyramid principle:
- **Unit Tests**: Test individual components and functions
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows

### 2. Testing Library Best Practices

\`\`\`jsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

test('should update user profile when form is submitted', async () => {
  const user = userEvent.setup();
  const mockUpdateUser = jest.fn();
  
  render(<UserProfileForm onUpdate={mockUpdateUser} />);
  
  await user.type(screen.getByLabelText(/name/i), 'John Doe');
  await user.click(screen.getByRole('button', { name: /save/i }));
  
  expect(mockUpdateUser).toHaveBeenCalledWith({ name: 'John Doe' });
});
\`\`\`

## Conclusion

Building scalable React applications requires careful consideration of architecture, state management, performance, and testing. By following these best practices and staying up-to-date with the React ecosystem, you'll be well-equipped to build applications that can grow and evolve with your needs.

Remember, scalability isn't just about handling more users or data—it's also about maintaining code quality and developer productivity as your team and codebase grow.
    `,
    slug: 'building-scalable-react-applications-2024',
    category: 'React',
    tags: ['React', 'JavaScript', 'Performance', 'Architecture'],
    publishedAt: '2024-01-15',
    readTime: 8,
    views: 12500,
    likes: 245,
    featured: true,
    image: '/images/blog/react-scalability.jpg',
    author: {
      name: 'RxY Developer',
      avatar: '/images/author-avatar.jpg',
    },
  },
  // Add more mock posts as needed
];

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const response = await blogApi.getPost(params.slug);
    const post = response.data.post;

    return {
      title: `${post.title} - ${siteConfig.name}`,
      description: post.excerpt,
      openGraph: {
        title: post.title,
        description: post.excerpt,
        url: `${siteConfig.url}/blog/${post.slug}`,
        siteName: siteConfig.name,
        type: 'article',
        publishedTime: new Date(post.publishedAt).toISOString(),
        authors: [post.author],
        tags: post.tags,
      },
      twitter: {
        card: 'summary_large_image',
        title: post.title,
        description: post.excerpt,
        creator: siteConfig.seo.twitter.creator,
      },
    };
  } catch (error) {
    return {
      title: 'Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }
}

export default async function BlogPostPageRoute({ params }: BlogPostPageProps) {
  try {
    const response = await blogApi.getPost(params.slug);
    const post = response.data.post;

    return <BlogPostPage post={post} />;
  } catch (error) {
    notFound();
  }
}
