import mongoose, { Schema, Document, Model } from 'mongoose';
import { generateId } from '@rxy/shared';
import type { BlogPost as BlogPostType } from '@rxy/shared';

export interface IBlogPost extends Omit<BlogPostType, 'id'>, Document {
  _id: string;
}

const blogPostSchema = new Schema<IBlogPost>(
  {
    _id: {
      type: String,
      default: () => generateId(),
    },
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
      index: true,
    },
    content: {
      type: String,
      required: true,
    },
    excerpt: {
      type: String,
      maxlength: 500,
    },
    featuredImage: {
      type: String,
      default: null,
    },
    tags: [{
      type: String,
      trim: true,
    }],
    category: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
    },
    seo: {
      metaTitle: {
        type: String,
        maxlength: 60,
      },
      metaDescription: {
        type: String,
        maxlength: 160,
      },
      keywords: [{
        type: String,
        trim: true,
      }],
    },
    author: {
      type: String,
      required: true,
      ref: 'User',
    },
    publishedAt: {
      type: Date,
      default: null,
    },
    views: {
      type: Number,
      default: 0,
    },
    likes: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
blogPostSchema.index({ slug: 1 });
blogPostSchema.index({ status: 1 });
blogPostSchema.index({ category: 1 });
blogPostSchema.index({ tags: 1 });
blogPostSchema.index({ publishedAt: -1 });
blogPostSchema.index({ createdAt: -1 });
blogPostSchema.index({ views: -1 });
blogPostSchema.index({ likes: -1 });

// Virtual for id
blogPostSchema.virtual('id').get(function () {
  return this._id;
});

// Ensure virtual fields are serialized
blogPostSchema.set('toJSON', {
  virtuals: true,
  transform: function (doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// Pre-save middleware to set publishedAt when status changes to published
blogPostSchema.pre('save', function (next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// Static methods
blogPostSchema.statics.findBySlug = function (slug: string) {
  return this.findOne({ slug: slug.toLowerCase() });
};

blogPostSchema.statics.findPublished = function () {
  return this.find({ status: 'published' }).sort({ publishedAt: -1 });
};

blogPostSchema.statics.findByCategory = function (category: string) {
  return this.find({ category, status: 'published' }).sort({ publishedAt: -1 });
};

blogPostSchema.statics.findByTag = function (tag: string) {
  return this.find({ tags: tag, status: 'published' }).sort({ publishedAt: -1 });
};

blogPostSchema.statics.search = function (query: string) {
  return this.find({
    $and: [
      { status: 'published' },
      {
        $or: [
          { title: { $regex: query, $options: 'i' } },
          { content: { $regex: query, $options: 'i' } },
          { excerpt: { $regex: query, $options: 'i' } },
          { tags: { $regex: query, $options: 'i' } },
        ],
      },
    ],
  }).sort({ publishedAt: -1 });
};

// Instance methods
blogPostSchema.methods.incrementViews = function () {
  this.views += 1;
  return this.save();
};

blogPostSchema.methods.incrementLikes = function () {
  this.likes += 1;
  return this.save();
};

export const BlogPost: Model<IBlogPost> = mongoose.model<IBlogPost>('BlogPost', blogPostSchema);
