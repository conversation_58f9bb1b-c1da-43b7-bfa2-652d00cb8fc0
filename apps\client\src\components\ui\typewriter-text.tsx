'use client';

import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useReducedMotion } from '@/hooks/use-reduced-motion';

interface TypewriterTextProps {
  texts: string[];
  className?: string;
  speed?: number;
  deleteSpeed?: number;
  delayBetween?: number;
  loop?: boolean;
}

export function TypewriterText({
  texts,
  className = '',
  speed = 100,
  deleteSpeed = 50,
  delayBetween = 2000,
  loop = true,
}: TypewriterTextProps) {
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  const [currentText, setCurrentText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isWaiting, setIsWaiting] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    if (prefersReducedMotion) {
      // Show the first text immediately for reduced motion
      setCurrentText(texts[0] || '');
      return;
    }

    let timeout: NodeJS.Timeout;

    const typeText = () => {
      const currentFullText = texts[currentTextIndex];
      
      if (isWaiting) {
        timeout = setTimeout(() => {
          setIsWaiting(false);
          setIsDeleting(true);
        }, delayBetween);
        return;
      }

      if (isDeleting) {
        if (currentText.length > 0) {
          setCurrentText(currentFullText.substring(0, currentText.length - 1));
          timeout = setTimeout(typeText, deleteSpeed);
        } else {
          setIsDeleting(false);
          setCurrentTextIndex((prev) => {
            if (loop) {
              return (prev + 1) % texts.length;
            } else {
              return Math.min(prev + 1, texts.length - 1);
            }
          });
          timeout = setTimeout(typeText, speed);
        }
      } else {
        if (currentText.length < currentFullText.length) {
          setCurrentText(currentFullText.substring(0, currentText.length + 1));
          timeout = setTimeout(typeText, speed);
        } else {
          if (loop || currentTextIndex < texts.length - 1) {
            setIsWaiting(true);
            timeout = setTimeout(typeText, delayBetween);
          }
        }
      }
    };

    timeout = setTimeout(typeText, speed);

    return () => clearTimeout(timeout);
  }, [
    currentTextIndex,
    currentText,
    isDeleting,
    isWaiting,
    texts,
    speed,
    deleteSpeed,
    delayBetween,
    loop,
    prefersReducedMotion,
  ]);

  return (
    <span className={className}>
      {currentText}
      <motion.span
        className="inline-block w-0.5 h-[1em] bg-current ml-1"
        animate={{ opacity: prefersReducedMotion ? 1 : [1, 1, 0, 0] }}
        transition={{
          duration: prefersReducedMotion ? 0 : 1,
          repeat: prefersReducedMotion ? 0 : Infinity,
          ease: 'easeInOut',
        }}
      />
    </span>
  );
}
