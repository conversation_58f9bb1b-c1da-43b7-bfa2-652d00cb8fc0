import { Router } from 'express';
import { BlogPost, BlogCategory, isDatabaseConnected } from '@rxy/database';
import { authenticate, authorize } from '@/middleware/auth';
import { validateBody, validateQuery } from '@/middleware/validation';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { MockBlogService } from '@/services/mockBlogService';
import { HTTP_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@rxy/shared';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize(['admin']));

// Get all blog posts (including drafts) with admin filters
router.get('/posts', 
  asyncHandler(async (req: any, res: any) => {
    const { 
      page = 1, 
      limit = 10, 
      status,
      category, 
      search,
      sort = 'createdAt'
    } = req.query;

    let result;
    
    if (isDatabaseConnected()) {
      const skip = (page - 1) * limit;
      let query: any = {};

      // Admin can see all statuses
      if (status) query.status = status;
      if (category) query.category = category;
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: 'i' } },
          { content: { $regex: search, $options: 'i' } },
          { excerpt: { $regex: search, $options: 'i' } },
          { tags: { $regex: search, $options: 'i' } },
        ];
      }

      let sortOption: any = {};
      switch (sort) {
        case 'createdAt':
          sortOption = { createdAt: -1 };
          break;
        case 'updatedAt':
          sortOption = { updatedAt: -1 };
          break;
        case 'publishedAt':
          sortOption = { publishedAt: -1 };
          break;
        case 'views':
          sortOption = { views: -1 };
          break;
        case 'title':
          sortOption = { title: 1 };
          break;
        default:
          sortOption = { createdAt: -1 };
      }

      const [posts, total] = await Promise.all([
        BlogPost.find(query)
          .sort(sortOption)
          .skip(skip)
          .limit(parseInt(limit))
          .populate('author', 'name email avatar')
          .lean(),
        BlogPost.countDocuments(query)
      ]);

      const totalPages = Math.ceil(total / limit);
      result = {
        posts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      };
    } else {
      // Use mock service for development
      const mockResult = await MockBlogService.getPosts({
        page, limit, category, search, sort
      });
      
      result = {
        posts: mockResult.posts,
        pagination: {
          page: mockResult.page,
          limit: mockResult.limit,
          total: mockResult.total,
          totalPages: mockResult.totalPages,
          hasNext: mockResult.page < mockResult.totalPages,
          hasPrev: mockResult.page > 1,
        },
      };
    }

    res.json({
      success: true,
      data: result,
    });
  })
);

// Get single blog post by ID (admin can see drafts)
router.get('/posts/:id', 
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    let post;

    if (isDatabaseConnected()) {
      post = await BlogPost.findById(id)
        .populate('author', 'name email avatar');
    } else {
      // For mock service, use slug as ID
      post = await MockBlogService.getPostBySlug(id);
    }

    if (!post) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    res.json({
      success: true,
      data: { post },
    });
  })
);

// Create new blog post
router.post('/posts',
  validateBody({
    title: 'string',
    slug: 'string',
    content: 'string',
    excerpt: 'string',
    featuredImage: 'string?',
    tags: 'array',
    category: 'string',
    status: 'string',
    seo: 'object?',
  }),
  asyncHandler(async (req: any, res: any) => {
    const postData = {
      ...req.body,
      author: req.user.id,
    };

    let post;

    if (isDatabaseConnected()) {
      post = new BlogPost(postData);
      await post.save();
      await post.populate('author', 'name email avatar');
    } else {
      // Mock creation for development
      post = {
        id: Date.now().toString(),
        ...postData,
        createdAt: new Date(),
        updatedAt: new Date(),
        views: 0,
        likes: 0,
      };
    }

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: SUCCESS_MESSAGES.CREATED,
      data: { post },
    });
  })
);

// Update blog post
router.put('/posts/:id',
  validateBody({
    title: 'string?',
    slug: 'string?',
    content: 'string?',
    excerpt: 'string?',
    featuredImage: 'string?',
    tags: 'array?',
    category: 'string?',
    status: 'string?',
    seo: 'object?',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    let post;

    if (isDatabaseConnected()) {
      post = await BlogPost.findById(id);
      if (!post) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }

      Object.assign(post, req.body);
      post.updatedAt = new Date();
      await post.save();
      await post.populate('author', 'name email avatar');
    } else {
      // Mock update for development
      post = {
        id,
        ...req.body,
        updatedAt: new Date(),
      };
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATED,
      data: { post },
    });
  })
);

// Delete blog post
router.delete('/posts/:id',
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    if (isDatabaseConnected()) {
      const post = await BlogPost.findById(id);
      if (!post) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }

      await BlogPost.findByIdAndDelete(id);
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.DELETED,
    });
  })
);

// Get blog categories (admin view)
router.get('/categories',
  asyncHandler(async (req: any, res: any) => {
    let categories;
    
    if (isDatabaseConnected()) {
      categories = await BlogCategory.find().sort({ name: 1 });
    } else {
      categories = await MockBlogService.getCategories();
    }

    res.json({
      success: true,
      data: { categories },
    });
  })
);

// Create blog category
router.post('/categories',
  validateBody({
    name: 'string',
    slug: 'string',
    description: 'string?',
    color: 'string?',
  }),
  asyncHandler(async (req: any, res: any) => {
    let category;

    if (isDatabaseConnected()) {
      category = new BlogCategory(req.body);
      await category.save();
    } else {
      // Mock creation for development
      category = {
        id: Date.now().toString(),
        ...req.body,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: SUCCESS_MESSAGES.CREATED,
      data: { category },
    });
  })
);

// Update blog category
router.put('/categories/:id',
  validateBody({
    name: 'string?',
    slug: 'string?',
    description: 'string?',
    color: 'string?',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    let category;

    if (isDatabaseConnected()) {
      category = await BlogCategory.findById(id);
      if (!category) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }

      Object.assign(category, req.body);
      category.updatedAt = new Date();
      await category.save();
    } else {
      // Mock update for development
      category = {
        id,
        ...req.body,
        updatedAt: new Date(),
      };
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATED,
      data: { category },
    });
  })
);

// Delete blog category
router.delete('/categories/:id',
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    if (isDatabaseConnected()) {
      const category = await BlogCategory.findById(id);
      if (!category) {
        throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
      }

      await BlogCategory.findByIdAndDelete(id);
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.DELETED,
    });
  })
);

export default router;
